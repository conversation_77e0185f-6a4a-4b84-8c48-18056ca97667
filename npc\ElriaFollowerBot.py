from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup

from telegram.ext import <PERSON><PERSON><PERSON>er, CommandHandler, ContextTypes



# Replace with your actual bot token from BotFather

BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"



# Sample quest data

quests = {

"forest": "🌲 A shadow stirs in the Whispering Woods. Investigate the source.",

"ruins": "🏛️ The Forgotten Ruins have begun to hum with ancient energy. Explore them."

}


async def start(update, context):
    await update.message.reply_text()

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text("Welcome, adventurer! Type /quest to receive your next challenge.")


await update.message.reply_text(

"Welcome, adventurer! Type /quest to receive your next challenge."

)



async def quest(update: Update, context: ContextTypes.DEFAULT_TYPE):

keyboard = [

[InlineKeyboardButton("Whispering Woods", callback_data='forest')],

[InlineKeyboardButton("Forgotten Ruins", callback_data='ruins')]

]

reply_markup = InlineKeyboardMarkup(keyboard)

await update.message.reply_text("Choose your quest:", reply_markup=reply_markup)



async def button(update: Update, context: ContextTypes.DEFAULT_TYPE):

query = update.callback_query

await query.answer()

quest_text = quests.get(query.data, "No quest found.")

await query.edit_message_text(text=f"🧭 Quest Accepted:\n{quest_text}")



if __name__ == '__main__':

app = ApplicationBuilder().token(BOT_TOKEN).build()

app.add_handler(CommandHandler("start", start))

app.add_handler(CommandHandler("quest", quest))

app.add_handler(telegram.ext.CallbackQueryHandler(button))

app.run_polling()