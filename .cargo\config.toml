# Cargo configuration for optimized builds

[build]
# Use default linker for better WSL compatibility
# rustflags = ["-C", "link-arg=-fuse-ld=lld"]

# Target-specific configurations for Linux
[target.x86_64-unknown-linux-gnu]
# Use default linker for better compatibility
# rustflags = ["-C", "link-arg=-fuse-ld=gold"]

# Faster builds in development
[profile.dev]
debug = 1
incremental = true

# Unstable features for faster compilation (requires nightly)
# [unstable]
# build-std = ["std", "panic_abort"]
# build-std-features = ["panic_immediate_abort"]
