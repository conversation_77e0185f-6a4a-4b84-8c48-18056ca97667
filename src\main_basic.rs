// Basic graphics version of The Dream Weaver's Heart
use bevy::prelude::*;

fn main() {
    println!("🎭 The Dream Weaver's Heart - Basic Graphics Version");
    
    App::new()
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                title: "The Dream Weaver's Heart".into(),
                resolution: (800.0, 600.0).into(),
                ..default()
            }),
            ..default()
        }))
        .add_systems(Startup, setup)
        .add_systems(Update, (input_system, story_system))
        .insert_resource(StoryState::new())
        .run();
}

#[derive(Resource)]
struct StoryState {
    chapter: usize,
    connection_strength: f32,
}

impl StoryState {
    fn new() -> Self {
        Self {
            chapter: 1,
            connection_strength: 30.0,
        }
    }
}

fn setup(mut commands: Commands, asset_server: Res<AssetServer>) {
    // Camera
    commands.spawn(Camera2dBundle::default());
    
    // Xing (The Weaver) - Blue
    commands.spawn((
        SpriteBundle {
            sprite: Sprite {
                color: Color::rgb(0.7, 0.9, 1.0),
                custom_size: Some(Vec2::new(50.0, 50.0)),
                ..default()
            },
            transform: Transform::from_xyz(-200.0, 0.0, 0.0),
            ..default()
        },
        Character { name: "Xing".to_string() },
    ));
    
    // Xerx (The Architect) - Orange
    commands.spawn((
        SpriteBundle {
            sprite: Sprite {
                color: Color::rgb(1.0, 0.8, 0.6),
                custom_size: Some(Vec2::new(50.0, 50.0)),
                ..default()
            },
            transform: Transform::from_xyz(200.0, 0.0, 0.0),
            ..default()
        },
        Character { name: "Xerx".to_string() },
    ));
    
    // The Heart - Pink
    commands.spawn((
        SpriteBundle {
            sprite: Sprite {
                color: Color::rgb(1.0, 0.6, 0.8),
                custom_size: Some(Vec2::new(30.0, 30.0)),
                ..default()
            },
            transform: Transform::from_xyz(0.0, 100.0, 0.0),
            ..default()
        },
        Character { name: "Heart".to_string() },
    ));
    
    // UI Text
    commands.spawn(
        TextBundle::from_section(
            "The Dream Weaver's Heart\nPress SPACE to advance story\nArrow keys to move characters",
            TextStyle {
                font_size: 20.0,
                color: Color::WHITE,
                ..default()
            },
        )
        .with_style(Style {
            position_type: PositionType::Absolute,
            top: Val::Px(10.0),
            left: Val::Px(10.0),
            ..default()
        }),
    );
    
    println!("✅ Game setup complete! Press SPACE to advance the story.");
}

#[derive(Component)]
struct Character {
    name: String,
}

fn input_system(
    keyboard_input: Res<Input<KeyCode>>,
    mut character_query: Query<&mut Transform, With<Character>>,
    mut story_state: ResMut<StoryState>,
) {
    // Story progression
    if keyboard_input.just_pressed(KeyCode::Space) {
        story_state.chapter += 1;
        story_state.connection_strength += 10.0;
        println!("📖 Chapter {} - Connection Strength: {:.1}", 
                story_state.chapter, story_state.connection_strength);
    }
    
    // Character movement
    let movement_speed = 2.0;
    for mut transform in character_query.iter_mut() {
        if keyboard_input.pressed(KeyCode::Left) {
            transform.translation.x -= movement_speed;
        }
        if keyboard_input.pressed(KeyCode::Right) {
            transform.translation.x += movement_speed;
        }
        if keyboard_input.pressed(KeyCode::Up) {
            transform.translation.y += movement_speed;
        }
        if keyboard_input.pressed(KeyCode::Down) {
            transform.translation.y -= movement_speed;
        }
    }
    
    if keyboard_input.just_pressed(KeyCode::Escape) {
        println!("👋 Thank you for experiencing The Dream Weaver's Heart!");
        std::process::exit(0);
    }
}

fn story_system(
    story_state: Res<StoryState>,
    mut character_query: Query<(&mut Sprite, &Character)>,
) {
    // Visual effects based on story progression
    for (mut sprite, character) in character_query.iter_mut() {
        match character.name.as_str() {
            "Xing" => {
                // Xing glows brighter as connection strengthens
                let glow = story_state.connection_strength / 100.0;
                sprite.color = Color::rgb(0.7 + glow * 0.3, 0.9, 1.0);
            }
            "Xerx" => {
                // Xerx becomes more vibrant
                let vibrance = story_state.connection_strength / 100.0;
                sprite.color = Color::rgb(1.0, 0.8 + vibrance * 0.2, 0.6);
            }
            "Heart" => {
                // Heart pulses with the story
                let pulse = (story_state.chapter as f32 * 0.5).sin() * 0.2 + 0.8;
                sprite.color = Color::rgb(1.0, 0.6 * pulse, 0.8 * pulse);
            }
            _ => {}
        }
    }
}
