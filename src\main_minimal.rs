// Minimal graphics test for The Dream Weaver's Heart
use bevy::prelude::*;

fn main() {
    println!("🎮 Starting minimal graphics test...");
    
    App::new()
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                title: "Dream Weaver Test".into(),
                resolution: (400.0, 300.0).into(),
                ..default()
            }),
            ..default()
        }))
        .add_systems(Startup, setup)
        .run();
}

fn setup(mut commands: Commands) {
    commands.spawn(Camera2dBundle::default());
    println!("✅ Minimal test running!");
}
