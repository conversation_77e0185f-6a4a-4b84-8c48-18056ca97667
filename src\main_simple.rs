// Simple test version to verify Rust compilation works
use bevy::prelude::*;

fn main() {
    println!("🎮 The Dream Weaver's Heart - Starting...");
    
    App::new()
        .add_plugins(DefaultPlugins.set(WindowPlugin {
            primary_window: Some(Window {
                title: "The Dream Weaver's Heart - Test".into(),
                resolution: (800.0, 600.0).into(),
                ..default()
            }),
            ..default()
        }))
        .add_systems(Startup, setup)
        .add_systems(Update, simple_system)
        .run();
}

fn setup(mut commands: Commands) {
    // Spawn a camera
    commands.spawn(Camera2dBundle::default());
    
    // Spawn a simple colored rectangle to test rendering
    commands.spawn(SpriteBundle {
        sprite: Sprite {
            color: Color::rgb(0.7, 0.9, 1.0),
            custom_size: Some(Vec2::new(100.0, 100.0)),
            ..default()
        },
        transform: Transform::from_xyz(0.0, 0.0, 0.0),
        ..default()
    });
    
    println!("✅ Game setup complete!");
}

fn simple_system(
    keyboard_input: Res<Input<KeyCode>>,
    mut query: Query<&mut Transform, With<Sprite>>,
) {
    if keyboard_input.pressed(KeyCode::Escape) {
        println!("👋 Goodbye from The Dream Weaver's Heart!");
        std::process::exit(0);
    }
    
    // Simple movement test
    for mut transform in query.iter_mut() {
        if keyboard_input.pressed(KeyCode::Left) {
            transform.translation.x -= 2.0;
        }
        if keyboard_input.pressed(KeyCode::Right) {
            transform.translation.x += 2.0;
        }
        if keyboard_input.pressed(KeyCode::Up) {
            transform.translation.y += 2.0;
        }
        if keyboard_input.pressed(KeyCode::Down) {
            transform.translation.y -= 2.0;
        }
    }
}
