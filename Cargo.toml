[package]
name = "epoch-of-elria"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "An optimized Rust game - Epoch of Elria"
license = "MIT OR Apache-2.0"

[dependencies]
# Core game engine - Bevy with Linux-compatible features
bevy = { version = "0.12", features = [
    "bevy_winit",      # Window management
    "bevy_render",     # Rendering system
    "bevy_sprite",     # 2D sprites
    "bevy_ui",         # UI system
    "bevy_text",       # Text rendering
    "png",             # PNG image support
    "x11",             # X11 support for Linux
    "wayland",         # Wayland support for Linux
], default-features = false }

# Performance and utility crates
rand = "0.8"           # Random number generation
serde = { version = "1.0", features = ["derive"] }  # Serialization
ron = "0.8"            # Rusty Object Notation for config files
anyhow = "1.0"         # Error handling
thiserror = "1.0"      # Error derive macros

# Optional: Physics engine (uncomment if needed)
# bevy_rapier2d = "0.23"  # 2D physics
# bevy_rapier3d = "0.23"  # 3D physics

[dev-dependencies]
criterion = "0.5"      # Benchmarking

# Optimization profiles
[profile.dev]
opt-level = 1          # Some optimization for better debug performance
debug = true           # Keep debug info

[profile.dev.package."*"]
opt-level = 3          # Optimize dependencies even in debug mode

[profile.release]
opt-level = 3          # Maximum optimization
lto = "thin"           # Link-time optimization
codegen-units = 1      # Better optimization at cost of compile time
panic = "abort"        # Smaller binary size
strip = true           # Remove debug symbols

# Fast compile profile for development
[profile.fast-dev]
inherits = "dev"
opt-level = 1
debug = false

# Distribution profile with maximum optimization
[profile.dist]
inherits = "release"
lto = "fat"            # Maximum link-time optimization
codegen-units = 1
panic = "abort"

[[bench]]
name = "game_benchmarks"
harness = false

# Workspace configuration (commented out for single-crate project)
# [workspace]
# members = [
#     ".",
#     "crates/*"
# ]
