const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000; // Use environment variable for port or default to 3000

app.use(express.json()); // Use built-in Express middleware for JSON parsing

// Example GET route
app.get('/api/status', (req, res) => {
  res.json({ status: 'Server is running', timestamp: new Date() });
});

app.post('/api/cgx', (req, res) => {
  const message = req.body.message;
  if (!message) {
    return res.status(400).send('Message is required');
  }
  console.log(`Received message from developer: ${message}`);
  // You can add code here to send response back to REST Client
  res.send(`CG<PERSON> received your message: ${message}`);
});

// Basic error handling middleware (add this at the end, after all routes)
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send('Something broke!');
});

app.listen(PORT, () => {
  console.log(`Server listening on port ${PORT}`);
});
