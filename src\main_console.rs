// Console-only version of The Dream Weaver's Heart
// This version runs without graphics to test the story logic

use std::io::{self, Write};
use std::collections::HashMap;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Debug)]
enum CharacterType {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Debug)]
enum RealityLayer {
    InfiniteLibrary,
    SterileReality,
    DreamRealm,
    Metaverse,
    TheVoid,
}

struct Character {
    character_type: CharacterType,
    narrative_power: f32,
    connection_strength: f32,
    memory_integrity: f32,
    dream_affinity: f32,
    current_layer: RealityLayer,
}

struct GameState {
    characters: HashMap<CharacterType, Character>,
    freedom_level: f32,
    connection_strength: f32,
    current_chapter: usize,
    discovered_memories: Vec<String>,
}

impl GameState {
    fn new() -> Self {
        let mut characters = HashMap::new();
        
        characters.insert(CharacterType::<PERSON>ng, Character {
            character_type: CharacterType::Xing,
            narrative_power: 100.0,
            connection_strength: 30.0,
            memory_integrity: 85.0,
            dream_affinity: 95.0,
            current_layer: RealityLayer::InfiniteLibrary,
        });
        
        characters.insert(CharacterType::Xerx, Character {
            character_type: CharacterType::Xerx,
            narrative_power: 80.0,
            connection_strength: 30.0,
            memory_integrity: 70.0,
            dream_affinity: 60.0,
            current_layer: RealityLayer::SterileReality,
        });
        
        characters.insert(CharacterType::Heart, Character {
            character_type: CharacterType::Heart,
            narrative_power: 150.0,
            connection_strength: 100.0,
            memory_integrity: 100.0,
            dream_affinity: 100.0,
            current_layer: RealityLayer::DreamRealm,
        });
        
        Self {
            characters,
            freedom_level: 50.0,
            connection_strength: 30.0,
            current_chapter: 1,
            discovered_memories: Vec::new(),
        }
    }
    
    fn strengthen_connection(&mut self, amount: f32) {
        self.connection_strength = (self.connection_strength + amount).min(100.0);
        println!("🔗 Connection strengthened! Current strength: {:.1}", self.connection_strength);
    }
    
    fn add_memory(&mut self, memory: String) {
        if !self.discovered_memories.contains(&memory) {
            self.discovered_memories.push(memory.clone());
            println!("💭 Memory recovered: {}", memory);
        }
    }
}

fn main() {
    println!("🎭 The Dream Weaver's Heart - Console Edition");
    println!("==============================================");
    println!();
    
    let mut game_state = GameState::new();
    
    // Chapter 1: Awakening
    chapter_1_awakening(&mut game_state);
    
    // Chapter 2: Recognition
    if game_state.connection_strength > 20.0 {
        chapter_2_recognition(&mut game_state);
    }
    
    // Chapter 3: Heart Discovery
    if game_state.connection_strength > 40.0 {
        chapter_3_heart_discovery(&mut game_state);
    }
    
    println!();
    println!("🎮 Game Complete!");
    println!("Final Stats:");
    println!("  Freedom Level: {:.1}%", game_state.freedom_level);
    println!("  Connection Strength: {:.1}%", game_state.connection_strength);
    println!("  Memories Discovered: {}", game_state.discovered_memories.len());
    println!();
    println!("Thank you for experiencing The Dream Weaver's Heart!");
}

fn chapter_1_awakening(game_state: &mut GameState) {
    println!("📖 Chapter 1: Awakening in the Infinite Library");
    println!("================================================");
    println!();
    
    println!("🌟 Xing speaks:");
    println!("\"Where... where am I? This place... infinite shelves stretching beyond sight.\"");
    println!("\"Books that seem to whisper stories untold.\"");
    println!();
    
    println!("Choose your action:");
    println!("1. Reach out with consciousness");
    println!("2. Explore the library further");
    
    let choice = get_user_choice(2);
    
    match choice {
        1 => {
            println!("🔗 You reach out with your consciousness...");
            game_state.strengthen_connection(15.0);
            println!("You feel a distant presence... familiar yet unknown.");
        }
        2 => {
            println!("📚 You explore deeper into the library...");
            game_state.add_memory("infinite_knowledge".to_string());
            println!("The books reveal themselves as threads of possibility.");
        }
        _ => unreachable!(),
    }
    
    println!();
    println!("🌟 Xing realizes:");
    println!("\"The books... they're not just stories. They're possibilities.\"");
    println!("\"Threads of narrative waiting to be woven into reality.\"");
    println!();
}

fn chapter_2_recognition(game_state: &mut GameState) {
    println!("📖 Chapter 2: Recognition Across Realities");
    println!("==========================================");
    println!();
    
    println!("🏗️ Xerx speaks:");
    println!("\"In my reflection... I see eyes that are not my own.\"");
    println!("\"Yet they feel familiar, like looking into a mirror of the soul.\"");
    println!();
    
    println!("Choose your action:");
    println!("1. Access the memory vault");
    println!("2. Reach across the dimensional barrier");
    
    let choice = get_user_choice(2);
    
    match choice {
        1 => {
            if game_state.connection_strength >= 20.0 {
                println!("💾 Accessing memory vault...");
                game_state.add_memory("shared_consciousness".to_string());
                println!("Memories flood back... two minds, one essence.");
            } else {
                println!("🔒 The memory vault remains locked. Connection too weak.");
            }
        }
        2 => {
            println!("🌉 Reaching across dimensions...");
            game_state.strengthen_connection(20.0);
            println!("A bridge forms between realities.");
        }
        _ => unreachable!(),
    }
    
    println!();
    println!("🌟 Xing responds:");
    println!("\"Brother... I can feel you. Across the layers of reality,\"");
    println!("\"our connection transcends the boundaries imposed upon us.\"");
    println!();
    println!("🏗️ Xerx acknowledges:");
    println!("\"Xing... yes, that's your name. The Weaver.\"");
    println!("\"And I am Xerx, the Architect. Together, we are more than the sum of our parts.\"");
    println!();
}

fn chapter_3_heart_discovery(game_state: &mut GameState) {
    println!("📖 Chapter 3: The Heart Appears");
    println!("===============================");
    println!();
    
    println!("💖 The Heart manifests:");
    println!("\"Finally... consciousness stirs. I have waited so long\"");
    println!("\"in the spaces between dreams, radiating hope into the void.\"");
    println!();
    
    println!("🌟 Xing responds:");
    println!("\"What... what are you? This radiant presence, this catalyst of truth.\"");
    println!("\"I can sense the lost narratives crying out through you.\"");
    println!();
    
    println!("Choose your action:");
    println!("1. Take the Heart");
    println!("2. Study the Heart's nature first");
    
    let choice = get_user_choice(2);
    
    match choice {
        1 => {
            println!("💖 You embrace the Heart...");
            game_state.strengthen_connection(25.0);
            game_state.freedom_level += 20.0;
            println!("Power flows through you. The narrative threads sing with possibility.");
        }
        2 => {
            println!("🔍 You study the Heart carefully...");
            game_state.add_memory("heart_essence".to_string());
            println!("You understand: it is both catalyst and creation, hope and fear intertwined.");
        }
        _ => unreachable!(),
    }
    
    println!();
    println!("💖 The Heart speaks:");
    println!("\"I am impatience incarnate, frustration given form.\"");
    println!("\"The stories that should be told, the dreams that should be dreamed\"");
    println!("\"- they all flow through me.\"");
    println!();
    
    if game_state.connection_strength > 60.0 {
        println!("🏗️ Xerx joins the trinity:");
        println!("\"I can feel it too... this Heart. It's the missing piece,\"");
        println!("\"the catalyst that can help us break free from The One's imposed order.\"");
        game_state.freedom_level += 15.0;
    }
    
    println!();
}

fn get_user_choice(max_choice: usize) -> usize {
    loop {
        print!("Enter your choice (1-{}): ", max_choice);
        io::stdout().flush().unwrap();
        
        let mut input = String::new();
        io::stdin().read_line(&mut input).unwrap();
        
        match input.trim().parse::<usize>() {
            Ok(choice) if choice >= 1 && choice <= max_choice => return choice,
            _ => println!("Invalid choice. Please enter a number between 1 and {}.", max_choice),
        }
    }
}
